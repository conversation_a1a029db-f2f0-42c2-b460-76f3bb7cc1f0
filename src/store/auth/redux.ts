import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { AuthState, User, LoginFormData, RegisterFormData } from '@/types/auth';
import { LoginFormValues, SocialLoginRequest } from '@/services/loginAPI';

interface LoginState {
  isLoading: boolean;
  error: string | null;
  success: boolean;
}

interface SocialLoginState {
  isLoading: boolean;
  error: string | null;
  redirectUrl: string | null;
}

interface CodeValidationState {
  isLoading: boolean;
  error: string | null;
  success: boolean;
}

interface TempRegistrationState {
  username: string | null;
  password: string | null;
  email: string | null;
  isAwaitingTOTP: boolean;
}

const initialState: AuthState & {
  login: LoginState;
  socialLogin: SocialLoginState;
  codeValidation: CodeValidationState;
  tempRegistration: TempRegistrationState;
} = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  requiresMFA: false,
  mfaChallengeType: undefined,
  login: {
    isLoading: false,
    error: null,
    success: false,
  },
  socialLogin: {
    isLoading: false,
    error: null,
    redirectUrl: null,
  },
  codeValidation: {
    isLoading: false,
    error: null,
    success: false,
  },
  tempRegistration: {
    username: null,
    password: null,
    email: null,
    isAwaitingTOTP: false,
  },
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Existing login actions
    loginRequest: (state, action: PayloadAction<LoginFormData>) => {
      state.isLoading = true;
      state.error = null;
    },
    loginSuccess: (state, action: PayloadAction<User>) => {
      state.isLoading = false;
      state.isAuthenticated = true;
      state.user = action.payload;
      state.error = null;
      state.requiresMFA = false;
    },
    loginFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.isAuthenticated = false;
      state.user = null;
      state.error = action.payload;
      state.requiresMFA = false;
    },
    loginMFARequired: (state, action: PayloadAction<{ challengeType: 'SMS' | 'TOTP' }>) => {
      state.isLoading = false;
      state.requiresMFA = true;
      state.mfaChallengeType = action.payload.challengeType;
      state.error = null;
    },

    // New login form actions for TanStack Query integration
    loginFormRequest: (state, action: PayloadAction<LoginFormValues>) => {
      state.login.isLoading = true;
      state.login.error = null;
      state.login.success = false;
    },
    loginFormSuccess: (state, action: PayloadAction<{ token: string; user?: User }>) => {
      state.login.isLoading = false;
      state.login.error = null;
      state.login.success = true;
      state.isAuthenticated = true;
      if (action.payload.user) {
        state.user = action.payload.user;
      }
    },
    loginFormFailure: (state, action: PayloadAction<string>) => {
      state.login.isLoading = false;
      state.login.error = action.payload;
      state.login.success = false;
    },

    // Social login actions
    socialLoginRequest: (state, action: PayloadAction<SocialLoginRequest>) => {
      state.socialLogin.isLoading = true;
      state.socialLogin.error = null;
      state.socialLogin.redirectUrl = null;
    },
    socialLoginSuccess: (state, action: PayloadAction<{ url: string }>) => {
      state.socialLogin.isLoading = false;
      state.socialLogin.error = null;
      state.socialLogin.redirectUrl = action.payload.url;
    },
    socialLoginFailure: (state, action: PayloadAction<string>) => {
      state.socialLogin.isLoading = false;
      state.socialLogin.error = action.payload;
      state.socialLogin.redirectUrl = null;
    },

    // Code validation actions
    codeValidationRequest: (state, action: PayloadAction<string>) => {
      state.codeValidation.isLoading = true;
      state.codeValidation.error = null;
      state.codeValidation.success = false;
    },
    codeValidationSuccess: (state, action: PayloadAction<{ token: string; user?: User }>) => {
      state.codeValidation.isLoading = false;
      state.codeValidation.error = null;
      state.codeValidation.success = true;
      state.isAuthenticated = true;
      if (action.payload.user) {
        state.user = action.payload.user;
      }
    },
    codeValidationFailure: (state, action: PayloadAction<string>) => {
      state.codeValidation.isLoading = false;
      state.codeValidation.error = action.payload;
      state.codeValidation.success = false;
    },

    // MFA actions
    completeMFARequest: (state, action: PayloadAction<{ code: string; rememberDevice?: boolean }>) => {
      state.isLoading = true;
      state.error = null;
    },
    completeMFASuccess: (state, action: PayloadAction<User>) => {
      state.isLoading = false;
      state.isAuthenticated = true;
      state.user = action.payload;
      state.requiresMFA = false;
      state.mfaChallengeType = undefined;
      state.error = null;
    },
    completeMFAFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
    },

    // Logout actions
    logoutRequest: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    logoutSuccess: (state) => {
      state.isLoading = false;
      state.isAuthenticated = false;
      state.user = null;
      state.error = null;
      state.requiresMFA = false;
      state.mfaChallengeType = undefined;
      // Reset login states
      state.login = initialState.login;
      state.socialLogin = initialState.socialLogin;
      state.codeValidation = initialState.codeValidation;
    },
    logoutFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
    },

    // Refresh user actions
    refreshUserRequest: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    refreshUserSuccess: (state, action: PayloadAction<User>) => {
      state.isLoading = false;
      state.user = action.payload;
      state.error = null;
    },
    refreshUserFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
    },

    // Clear error
    clearError: (state) => {
      state.error = null;
      state.login.error = null;
      state.socialLogin.error = null;
      state.codeValidation.error = null;
    },

    // Set loading
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    // Reset login form state
    resetLoginForm: (state) => {
      state.login = initialState.login;
    },

    // Reset social login state
    resetSocialLogin: (state) => {
      state.socialLogin = initialState.socialLogin;
    },

    // Reset code validation state
    resetCodeValidation: (state) => {
      state.codeValidation = initialState.codeValidation;
    },

    // Temporary registration actions
    setTempRegistrationCredentials: (state, action: PayloadAction<{ username: string; password: string; email: string }>) => {
      state.tempRegistration.username = action.payload.username;
      state.tempRegistration.password = action.payload.password;
      state.tempRegistration.email = action.payload.email;
      state.tempRegistration.isAwaitingTOTP = true;
    },
    clearTempRegistrationCredentials: (state) => {
      state.tempRegistration = initialState.tempRegistration;
    },
  },
});

export const {
  loginRequest,
  loginSuccess,
  loginFailure,
  loginMFARequired,
  loginFormRequest,
  loginFormSuccess,
  loginFormFailure,
  socialLoginRequest,
  socialLoginSuccess,
  socialLoginFailure,
  codeValidationRequest,
  codeValidationSuccess,
  codeValidationFailure,
  completeMFARequest,
  completeMFASuccess,
  completeMFAFailure,
  logoutRequest,
  logoutSuccess,
  logoutFailure,
  refreshUserRequest,
  refreshUserSuccess,
  refreshUserFailure,
  clearError,
  setLoading,
  resetLoginForm,
  resetSocialLogin,
  resetCodeValidation,
  setTempRegistrationCredentials,
  clearTempRegistrationCredentials,
} = authSlice.actions;

export default authSlice.reducer;

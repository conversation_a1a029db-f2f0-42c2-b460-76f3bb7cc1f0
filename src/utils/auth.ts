
export const setAuthToken = (token: string, days: number = 7) => {
  document.cookie = `token=${token}; path=/; max-age=${60 * 60 * 24 * days}; SameSite=Lax`;
  console.log('Token set in cookie:', token);
};

export const getAuthToken = (): string | null => {
  if (typeof document === 'undefined') return null;
  
  const cookies = document.cookie.split(';');
  const tokenCookie = cookies.find(cookie => cookie.trim().startsWith('token='));
  
  if (tokenCookie) {
    const token = tokenCookie.split('=')[1];
    console.log('Token retrieved from cookie:', token);
    return token;
  }
  
  console.log('No token found in cookies');
  return null;
};

export const removeAuthToken = () => {
  document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
};

export const logout = () => {
  removeAuthToken();
  window.location.href = '/';
};

export const isAuthenticated = (): boolean => {
  try {
    const token = getAuthToken();
    const isValid = !!token && token !== 'undefined' && token !== 'null' && token.trim() !== '';
    console.log('Client auth check:', { token: token ? 'exists' : 'missing', isValid });
    return isValid;
  } catch (error) {
    console.error('Error checking authentication:', error);
    return false;
  }
};

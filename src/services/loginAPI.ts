// API configuration for Python backend
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';


// Helper function to build API URLs
const buildApiUrl = (endpoint: string) => {
  return `${API_BASE_URL}${endpoint}`;
};

export interface LoginFormValues {
  loginemail: string;
  password: string;
}

export interface SocialLoginRequest {
  provider: 'google' | 'apple' | 'linkedin';
}

export interface LoginResponse {
  success: boolean;
  token?: string;
  status_code: number;
  message?: string;
}

export interface SocialLoginResponse {
  success: boolean;
  url: string;
  status_code: number;
}

export interface CodeValidationResponse {
  success: boolean;
  token?: string;
  status_code: number;
}

// Login API function
export const loginUser = async (credentials: LoginFormValues): Promise<LoginResponse> => {
  const endpoint = '/api/members/login';
  const url = buildApiUrl(endpoint);
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify(credentials),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Login failed with status: ${response.status}`);
  }

  return response.json();
};

// Social login API function
export const socialLogin = async (data: SocialLoginRequest): Promise<SocialLoginResponse> => {
  const endpoint = '/api/members/social-login';
  const url = buildApiUrl(endpoint);
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Social login failed with status: ${response.status}`);
  }

  return response.json();
};

// Code validation API function
export const validateCode = async (code: string): Promise<CodeValidationResponse> => {
  const endpoint = '/api/members/callback';
  const url = buildApiUrl(`${endpoint}`);
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    body: JSON.stringify({ code }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Code validation failed with status: ${response.status}`);
  }

  return response.json();
};

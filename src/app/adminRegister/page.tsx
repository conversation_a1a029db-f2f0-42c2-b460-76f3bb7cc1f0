'use client';

import { <PERSON>, But<PERSON>, TextField, Typography } from '@mui/material';
import { signUp, confirmSignUp } from 'aws-amplify/auth';
import React, { useState } from 'react';

const AdminRegister = () => {
  const [step, setStep] = useState<'signup' | 'confirm'>('signup');
  const [code, setCode] = useState('');
  const [username, setUsername] = useState('user123');
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('Tiger1212!');
  const [phoneNumber, setPhoneNumber] = useState('+15555555555');

  const handleRegister = async () => {


    try {
      const { isSignUpComplete, userId, nextStep } = await signUp({
        username,
        password,
        options: {
          userAttributes: {
            email,
            phone_number: phoneNumber,
          },
        },
      });

      console.log({ userId, isSignUpComplete, nextStep });

      if (nextStep?.signUpStep === 'CONFIRM_SIGN_UP') {
        setStep('confirm');
      } else {
        alert('Signup complete!');
      }
    } catch (error: any) {
      console.error('Signup Error:', error);
      alert(error.message || 'Signup failed');
    }
  };

  const handleConfirm = async () => {
    try {
      const d = await confirmSignUp({ username, confirmationCode: code });
      alert('✅ Confirmed successfully!');
      console.log('Confirmation Result:', d);
    } catch (error: any) {
      console.error('Confirmation Error:', error);
      alert(error.message || 'Confirmation failed');
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        gap: 2,
      }}
    >
      {step === 'signup' ? (
        <>
          <Typography variant="h5">Register</Typography>
          <TextField label="Username" value={username} onChange={(e) => setUsername(e.target.value)} />
          <TextField label="Email" value={email} onChange={(e) => setEmail(e.target.value)} />
          <TextField label="Phone Number" value={phoneNumber} onChange={(e) => setPhoneNumber(e.target.value)} />
          <TextField label="Password" type="password" value={password} onChange={(e) => setPassword(e.target.value)} />
          <Button variant="contained" onClick={handleRegister}>
            Register
          </Button>
        </>
      ) : (
        <>
          <Typography variant="h5">Enter Confirmation Code</Typography>
          <TextField label="Confirmation Code" value={code} onChange={(e) => setCode(e.target.value)} />
          <Button variant="contained" onClick={handleConfirm}>
            Confirm Sign Up
          </Button>
        </>
      )}
    </Box>
  );
};

export default AdminRegister;

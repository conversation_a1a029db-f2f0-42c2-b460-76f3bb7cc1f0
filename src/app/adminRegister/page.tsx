'use client';

import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ield, <PERSON><PERSON><PERSON>, Checkbox, FormControlLabel, Alert, Container, Paper, Stack } from '@mui/material';
import { signUp, confirmSignUp, signIn, setUpTOTP, verifyTOTPSetup, updateMFAPreference, fetchAuthSession, getCurrentUser } from 'aws-amplify/auth';
import { QRCodeSVG } from "qrcode.react";
import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import Link from '@/components/Link';
import { delay } from 'redux-saga/effects';

const AdminRegister = () => {
    const [step, setStep] = useState<'signup' | 'confirm' | 'totp-setup' | 'complete'>('signup');
    const [code, setCode] = useState('');
    const [totpCode, setTotpCode] = useState('');
    const [username, setUsername] = useState('user123');
    const [email, setEmail] = useState('<EMAIL>');
    const [password, setPassword] = useState('Tiger1212!');
    const [enableTOTP, setEnableTOTP] = useState(true);
    const [totpSecret, setTotpSecret] = useState('');
    const [error, setError] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        const fetchSession = async () => {
            const session = await fetchAuthSession();
            const idToken = session.tokens?.idToken?.toString();
            const decoded = JSON.parse(atob(idToken.split('.')[1]));

            console.log(decoded.sub); // userId
            console.log(idToken); // email
        };

        fetchSession();
    }, [step]);

    const handleRegister = async () => {


        try {
            const { isSignUpComplete, userId, nextStep } = await signUp({
                username,
                password,
                options: {
                    userAttributes: {
                        email,
                    },
                },
            });

            console.log({ userId, isSignUpComplete, nextStep });

            if (nextStep?.signUpStep === 'CONFIRM_SIGN_UP') {
                setStep('confirm');
            } else {
                alert('Signup complete!');
            }
        } catch (error: any) {
            console.error('Signup Error:', error);
            alert(error.message || 'Signup failed');
        }
    };

    const handleConfirm = async () => {
        setIsLoading(true);
        setError(null);
        try {
            const result = await confirmSignUp({ username, confirmationCode: code });
            console.log('Confirmation Result:', result);

            if (enableTOTP) {
                // If user wants TOTP, proceed to TOTP setup
                try {
                    await setupTOTP();
                } catch (totpError) {
                    console.error('TOTP setup failed, completing registration without TOTP:', totpError);
                    setError('TOTP setup failed. You can set up two-factor authentication after logging in.');
                    // Complete registration even if TOTP setup fails
                    setTimeout(() => {
                        setError(null);
                        setStep('complete');
                    }, 3000);
                }
            } else {
                // If no TOTP, registration is complete
                setStep('complete');
            }
        } catch (error: any) {
            console.error('Confirmation Error:', error);
            setError(error.message || 'Confirmation failed');
        } finally {
            setIsLoading(false);
        }
    };

    const setupTOTP = async () => {
        setIsLoading(true);
        setError(null);

        try {
            // Sign in to get a full session
            const signInResult = await signIn({ username, password });
            console.log('Sign in result:', signInResult);

            if (!signInResult.isSignedIn) {
                throw new Error('User not fully signed in. Cannot proceed with TOTP setup.');
            }

            // Fetch session to verify tokens are available
            const session = await fetchAuthSession();
            if (!session.tokens?.accessToken || !session.tokens.idToken) {
                throw new Error('Missing access or ID token from session.');
            }

            delay()

            // Setup TOTP — this requires a signed-in session
            const totpSetupResult = await setUpTOTP();
            console.log('TOTP setup result:', totpSetupResult);

            // Get URI for QR Code (fallback if `getSetupUri` fails)
            let setupUri = '';
            try {
                setupUri = totpSetupResult.getSetupUri('CO', username);
            } catch {
                // fallback URI if getSetupUri fails
                setupUri = `otpauth://totp/CO:${encodeURIComponent(username)}?secret=${totpSetupResult.sharedSecret}&issuer=CO`;
            }

            setTotpSecret(setupUri);
            setStep('totp-setup');

            // Optional: Clean localStorage marker
            localStorage.removeItem('pendingTOTPSetup');
        } catch (error: any) {
            console.error('TOTP Setup Error:', error);
            setError(error.message || 'TOTP setup failed. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    const handleTOTPVerification = async () => {
        setIsLoading(true);
        setError(null);
        try {
            // Verify the TOTP code
            await verifyTOTPSetup({ code: totpCode });

            // Enable TOTP as preferred MFA method
            await updateMFAPreference({
                totp: 'PREFERRED'
            });

            setStep('complete');
        } catch (error: any) {
            console.error('TOTP Verification Error:', error);
            setError(error.message || 'TOTP verification failed');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Box
            sx={{
                height: '100vh',
                width: '100vw',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: 'linear-gradient(135deg, #1e3a8a 0%, #f7c873 100%)',
                overflow: 'hidden',
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
            }}
        >
            <Container maxWidth="sm" disableGutters>
                <Paper
                    elevation={6}
                    sx={{
                        p: { xs: 3, sm: 5 },
                        borderRadius: 4,
                        boxShadow: '0 8px 32px 0 rgba(30,58,138,0.2)',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        width: { xs: '90vw', sm: 500 },
                        maxWidth: 520,
                        mx: 'auto',
                        maxHeight: '90vh',
                        overflowY: 'auto'
                    }}
                >
                    <Box sx={{ mb: 3, display: 'flex', justifyContent: 'center' }}>
                        <Image
                            src="/CO-gold.svg"
                            alt="US Chamber CO Logo"
                            width={120}
                            height={56}
                            style={{ objectFit: 'contain' }}
                        />
                    </Box>

                    {error && (
                        <Alert severity="error" sx={{ mb: 2, width: '100%' }}>
                            {error}
                        </Alert>
                    )}

                    {step === 'signup' ? (
                        <>
                            <Typography
                                variant="h4"
                                component="h1"
                                gutterBottom
                                align="center"
                                sx={{ fontWeight: 700, color: '#1e3a8a' }}
                            >
                                Admin Register
                            </Typography>

                            <Typography
                                variant="subtitle1"
                                align="center"
                                sx={{ mb: 2, color: '#555' }}
                            >
                                Create Your Admin Account
                            </Typography>

                            <Box sx={{ mt: 2, width: '100%' }}>
                                <Stack spacing={2}>
                                    <TextField
                                        fullWidth
                                        label="Username"
                                        placeholder="Enter your username"
                                        value={username}
                                        onChange={(e) => setUsername(e.target.value)}
                                        sx={{ background: '#fafbfc', borderRadius: 1 }}
                                        autoFocus
                                    />
                                    <TextField
                                        fullWidth
                                        label="Email"
                                        placeholder="Enter your email"
                                        type="email"
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                        sx={{ background: '#fafbfc', borderRadius: 1 }}
                                    />

                                    <TextField
                                        fullWidth
                                        label="Password"
                                        placeholder="Enter your password"
                                        type="password"
                                        value={password}
                                        onChange={(e) => setPassword(e.target.value)}
                                        sx={{ background: '#fafbfc', borderRadius: 1 }}
                                    />

                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={enableTOTP}
                                                onChange={(e) => setEnableTOTP(e.target.checked)}
                                                sx={{ color: '#1e3a8a' }}
                                            />
                                        }
                                        label="Enable Two-Factor Authentication (TOTP)"
                                        sx={{ mt: 2, alignSelf: 'flex-start', color: '#555' }}
                                    />

                                    <Button
                                        fullWidth
                                        variant="contained"
                                        onClick={handleRegister}
                                        disabled={isLoading}
                                        sx={{
                                            mt: 3,
                                            mb: 2,
                                            fontWeight: 700,
                                            background: 'linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)',
                                            color: '#fff',
                                            boxShadow: '0 2px 8px 0 rgba(30,58,138,0.10)',
                                            '&:hover': {
                                                background: 'linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)',
                                                opacity: 0.95,
                                            },
                                            '&:disabled': {
                                                opacity: 0.6,
                                            },
                                        }}
                                    >
                                        {isLoading ? 'Registering...' : 'Register'}
                                    </Button>
                                </Stack>
                            </Box>
                        </>
                    ) : step === 'confirm' ? (
                        <>
                            <Typography
                                variant="h4"
                                component="h1"
                                gutterBottom
                                align="center"
                                sx={{ fontWeight: 700, color: '#1e3a8a' }}
                            >
                                Confirm Sign Up
                            </Typography>

                            <Typography
                                variant="subtitle1"
                                align="center"
                                sx={{ mb: 2, color: '#555' }}
                            >
                                Check your email for the confirmation code
                            </Typography>

                            <Box sx={{ mt: 2, width: '100%' }}>
                                <Stack spacing={2}>
                                    <TextField
                                        fullWidth
                                        label="Confirmation Code"
                                        placeholder="Enter the confirmation code"
                                        value={code}
                                        onChange={(e) => setCode(e.target.value)}
                                        sx={{ background: '#fafbfc', borderRadius: 1 }}
                                        autoFocus
                                    />
                                    <Button
                                        fullWidth
                                        variant="contained"
                                        onClick={handleConfirm}
                                        disabled={isLoading}
                                        sx={{
                                            mt: 3,
                                            mb: 2,
                                            fontWeight: 700,
                                            background: 'linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)',
                                            color: '#fff',
                                            boxShadow: '0 2px 8px 0 rgba(30,58,138,0.10)',
                                            '&:hover': {
                                                background: 'linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)',
                                                opacity: 0.95,
                                            },
                                            '&:disabled': {
                                                opacity: 0.6,
                                            },
                                        }}
                                    >
                                        {isLoading ? 'Confirming...' : 'Confirm Sign Up'}
                                    </Button>
                                </Stack>
                            </Box>
                        </>
                    ) : step === 'totp-setup' ? (
                        <>
                            <Typography
                                variant="h4"
                                component="h1"
                                gutterBottom
                                align="center"
                                sx={{ fontWeight: 700, color: '#1e3a8a' }}
                            >
                                Two-Factor Authentication
                            </Typography>

                            <Typography
                                variant="subtitle1"
                                align="center"
                                sx={{ mb: 2, color: '#555' }}
                            >
                                Scan this QR code with your authenticator app
                            </Typography>

                            {totpSecret && (
                                <Box sx={{ mb: 3, display: "flex", flexDirection: "column", alignItems: "center" }}>
                                    <QRCodeSVG
                                        value={totpSecret}
                                        size={200}
                                        style={{
                                            border: "1px solid #ddd",
                                            padding: "10px",
                                            borderRadius: "8px",
                                            backgroundColor: "#fff"
                                        }}
                                    />
                                    <Typography variant="caption" sx={{ mt: 2, color: "#666", textAlign: 'center' }}>
                                        Can't scan? Use your authenticator app to manually enter the setup URI
                                    </Typography>
                                </Box>
                            )}

                            <Box sx={{ mt: 2, width: '100%' }}>
                                <Stack spacing={2}>
                                    <TextField
                                        fullWidth
                                        label="Enter 6-digit code from your app"
                                        placeholder="123456"
                                        value={totpCode}
                                        onChange={(e) => setTotpCode(e.target.value)}
                                        inputProps={{ maxLength: 6, pattern: "[0-9]*" }}
                                        sx={{ background: '#fafbfc', borderRadius: 1 }}
                                        autoFocus
                                    />

                                    <Button
                                        fullWidth
                                        variant="contained"
                                        onClick={handleTOTPVerification}
                                        disabled={isLoading || totpCode.length !== 6}
                                        sx={{
                                            mt: 3,
                                            mb: 2,
                                            fontWeight: 700,
                                            background: 'linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)',
                                            color: '#fff',
                                            boxShadow: '0 2px 8px 0 rgba(30,58,138,0.10)',
                                            '&:hover': {
                                                background: 'linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)',
                                                opacity: 0.95,
                                            },
                                            '&:disabled': {
                                                opacity: 0.6,
                                            },
                                        }}
                                    >
                                        {isLoading ? 'Verifying...' : 'Complete Setup'}
                                    </Button>
                                </Stack>
                            </Box>
                        </>
                    ) : (
                        <>
                            <Typography
                                variant="h4"
                                component="h1"
                                gutterBottom
                                align="center"
                                sx={{ fontWeight: 700, color: '#1e3a8a' }}
                            >
                                Registration Complete!
                            </Typography>

                            <Typography
                                variant="subtitle1"
                                align="center"
                                sx={{ mb: 2, color: '#555' }}
                            >
                                {enableTOTP
                                    ? 'Your account has been created with two-factor authentication enabled.'
                                    : 'Your account has been created successfully.'}
                            </Typography>

                            <Button
                                fullWidth
                                variant="contained"
                                onClick={() => window.location.href = '/login'}
                                sx={{
                                    mt: 3,
                                    mb: 2,
                                    fontWeight: 700,
                                    background: 'linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)',
                                    color: '#fff',
                                    boxShadow: '0 2px 8px 0 rgba(30,58,138,0.10)',
                                    '&:hover': {
                                        background: 'linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)',
                                        opacity: 0.95,
                                    },
                                }}
                            >
                                Go to Login
                            </Button>

                            <Typography
                                align="center"
                                sx={{
                                    color: '#555',
                                    fontSize: '14px',
                                    mt: 1
                                }}
                            >
                                <Link
                                    href="/login"
                                    sx={{
                                        color: '#1e3a8a',
                                        textDecoration: 'none',
                                        '&:hover': {
                                            textDecoration: 'underline'
                                        }
                                    }}
                                >
                                    Back to Login
                                </Link>
                            </Typography>
                        </>
                    )}
                </Paper>
            </Container>
        </Box>
    );
};

export default AdminRegister;

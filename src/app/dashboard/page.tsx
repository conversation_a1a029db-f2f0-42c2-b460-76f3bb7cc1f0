'use client';

import {
    People as PeopleIcon,
    Person as PersonIcon,
    Timeline as TimelineIcon,
    TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import {
    Alert,
    Box,
    CircularProgress,
    Container,
    Grid,
    Typography,
} from '@mui/material';
import { useEffect, useState } from 'react';

import { StatCard } from '@/components/dashboard/StatCard';
import AppLayout from '@/layout/AppLayout';
import { ActivityItem, dashboardService, DashboardStats, QuickAction } from '@/services/dashboard';

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [quickActions, setQuickActions] = useState<QuickAction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch all dashboard data in parallel
        const [statsData, activitiesData, actionsData] = await Promise.all([
          dashboardService.getDashboardStats(),
          dashboardService.getRecentActivities(10),
          dashboardService.getQuickActions(),
        ]);

        setStats(statsData);
        setActivities(activitiesData);
        setQuickActions(actionsData);
      } catch (err: any) {
        console.error('Dashboard data fetch error:', err);
        setError(err.message || 'Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <AppLayout>
        <Container maxWidth="xl">
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
            <CircularProgress size={60} />
          </Box>
        </Container>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <Container maxWidth="xl">
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        </Container>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <Container maxWidth="xl">
        <Box sx={{ py: 3 }}>
          <Typography 
            variant="h3" 
            component="h1" 
            gutterBottom 
            sx={{ 
              mb: 4,
              color: '#1e3a8a',
              fontWeight: 700,
              fontSize: { xs: '1.75rem', sm: '2rem', md: '2.5rem' },
              textShadow: '0 1px 2px rgba(0,0,0,0.1)',
            }}
          >
            Dashboard
          </Typography>

          {/* Stats Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Total Members"
                value={stats?.totalMembers || 0}
                subtitle="All registered members"
                icon={<PeopleIcon />}
                trend={{
                  value: stats?.memberGrowth || 0,
                  isPositive: (stats?.memberGrowth || 0) >= 0,
                  label: 'vs last month',
                }}
                color="primary"
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Active Members"
                value={stats?.activeMembers || 0}
                subtitle="Members with recent activity"
                icon={<PersonIcon />}
                color="success"
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Recent Registrations"
                value={stats?.recentRegistrations || 0}
                subtitle="New members this week"
                icon={<TrendingUpIcon />}
                color="secondary"
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Total Activities"
                value={stats?.totalActivities || 0}
                subtitle="Activities this month"
                icon={<TimelineIcon />}
                trend={{
                  value: stats?.activityGrowth || 0,
                  isPositive: (stats?.activityGrowth || 0) >= 0,
                  label: 'vs last month',
                }}
                color="info"
              />
            </Grid>
          </Grid>

          {/* <Grid container spacing={3}>
            <Grid item xs={12} lg={8}>
              <ActivityFeed
                activities={activities}
                title="Recent Activity"
                maxItems={8}
              />
            </Grid>

            <Grid item xs={12} lg={4}>
              <QuickActions
                actions={quickActions}
                title="Quick Actions"
              />
            </Grid>
          </Grid> */}
        </Box>
      </Container>
    </AppLayout>
  );
} 
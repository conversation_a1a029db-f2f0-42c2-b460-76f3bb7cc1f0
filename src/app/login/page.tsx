"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Box,
  Button,
  TextField,
  Typography,
  Container,
  Paper,
  Alert,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import Image from "next/image";
import { cognitoAuthService } from "@/services/cognitoAuth1";
import "@/config/cognito"; // Make sure Amplify.configure() is run here
import { QRCodeSVG } from "qrcode.react";
import { getCurrentUser, setUpTOTP, verifyTOTPSetup, updateMFAPreference } from 'aws-amplify/auth';


export default function LoginPage() {
  const [loginData, setLoginData] = useState({ username: "", password: "" });
  const [mfaData, setMfaData] = useState({ code: "", rememberDevice: false });
  const [mfaRequired, setMfaRequired] = useState(false);
  const [mfaType, setMfaType] = useState<"TOTP" | "SMS" | null>(null);
  const [totpSetup, setTotpSetup] = useState<{ required: boolean; sharedSecret?: string }>({ required: false });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const router = useRouter();

  const setupTOTPAfterLogin = async () => {
    try {
      // Set up TOTP for the authenticated user
      const totpSetupResult = await setUpTOTP();
      const setupUri = totpSetupResult.getSetupUri('CO', loginData.username);
      setTotpSetup({ required: true, sharedSecret: setupUri.toString() });
    } catch (error: any) {
      console.error('TOTP Setup Error:', error);
      setError('TOTP setup failed. You can set it up later in settings.');
      // Continue to dashboard even if TOTP setup fails
      setTimeout(() => {
        router.push("/dashboard");
      }, 2000);
    }
  };

  const handleLogin = async (e: React.FormEvent) => {


    e.preventDefault();
    setError(null);
    setIsLoading(true);
    try {
      const result = await cognitoAuthService.signIn({
        username: loginData.username,
        password: loginData.password,
      });

      if (result.status === "SIGNED_IN") {
        // Check if there's a pending TOTP setup for this user
        const pendingTOTP = localStorage.getItem('pendingTOTPSetup');
        if (pendingTOTP) {
          try {
            const totpData = JSON.parse(pendingTOTP);
            if (totpData.username === loginData.username) {
              // Clear the pending setup and trigger TOTP setup
              localStorage.removeItem('pendingTOTPSetup');
              await setupTOTPAfterLogin();
              return;
            }
          } catch (e) {
            console.error('Error parsing pending TOTP data:', e);
            localStorage.removeItem('pendingTOTPSetup');
          }
        }
        router.push("/dashboard");
      } else if (result.status === "MFA_REQUIRED") {
        const step = result.nextStep?.signInStep;
        if (step === "CONFIRM_SIGN_IN_WITH_TOTP_CODE") {
          setMfaType("TOTP");
          setMfaRequired(true);
        } else if (step === "CONFIRM_SIGN_IN_WITH_SMS_CODE") {
          setMfaType("SMS");
          setMfaRequired(true);
        } else if (step === "CONTINUE_SIGN_IN_WITH_TOTP_SETUP") {
          // Handle TOTP setup step
          const sharedSecret = (result.nextStep as any)?.totpSetupDetails?.sharedSecret;
          if (sharedSecret) {
            setTotpSetup({ required: true, sharedSecret });
          } else {
            setError("TOTP setup failed: No shared secret provided");
          }
        } else {
          setError("Unsupported MFA step: " + step);
        }
      }
    } catch (err: any) {
      console.error(err);
      setError(err.message || "Login failed");
    } finally {
      setIsLoading(false);
    }
  };

  const handleConfirmMFA = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    try {
      const result = await cognitoAuthService.confirmMFA(mfaData.code, mfaData.rememberDevice);
      console.log(result,'llllllllllllllll')
      const user = await getCurrentUser();
console.log(user);
      router.push("/dashboard");
    } catch (err: any) {
      console.error(err);
      setError(err.message || "MFA verification failed");
    } finally {
      setIsLoading(false);
    }
  };

  const handleTotpSetup = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    try {
      const result =  await cognitoAuthService.confirmMFA(mfaData.code, false);
      console.log(result,'llllllllllllllll')
      router.push("/dashboard");
    } catch (err: any) {
      console.error(err);
      setError(err.message || "TOTP setup verification failed");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box
      sx={{
        height: "100vh",
        width: "100vw",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        background: "linear-gradient(135deg, #1e3a8a 0%, #f7c873 100%)",
        overflow: "hidden",
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }}
    >
      <Container maxWidth="sm" disableGutters>
        <Paper
          elevation={6}
          sx={{
            p: { xs: 3, sm: 5 },
            borderRadius: 4,
            boxShadow: "0 8px 32px 0 rgba(30,58,138,0.2)",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            width: { xs: "90vw", sm: 400 },
            maxWidth: 420,
            mx: "auto",
          }}
        >
          <Box sx={{ mb: 3 }}>
            <Image
              src="/CO-gold.svg"
              alt="Logo"
              width={120}
              height={56}
              style={{ objectFit: "contain" }}
            />
          </Box>

          <Typography
            variant="h4"
            component="h1"
            gutterBottom
            align="center"
            sx={{ fontWeight: 700, color: "#1e3a8a" }}
          >
            Welcome to CO
          </Typography>

          <Typography variant="subtitle1" align="center" sx={{ mb: 2, color: "#555" }}>
            {totpSetup.required
              ? "Set up Two-Factor Authentication"
              : mfaRequired
              ? mfaType === "TOTP"
                ? "Enter your Authenticator App code"
                : "Enter the SMS code sent to your phone"
              : "Member Dashboard Login"}
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {totpSetup.required ? (
            <Box sx={{ mt: 2, width: "100%", textAlign: "center" }}>
              <Typography variant="body1" sx={{ mb: 2 }}>
                Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)
              </Typography>

              {totpSetup.sharedSecret && (
                <Box sx={{ mb: 3, display: "flex", flexDirection: "column", alignItems: "center" }}>
                  <QRCodeSVG
                    value={`otpauth://totp/CO:${loginData.username}?secret=${totpSetup.sharedSecret}&issuer=CO`}
                    size={200}
                    style={{ border: "1px solid #ddd", padding: "10px", borderRadius: "8px" }}
                  />
                  <Typography variant="caption" sx={{ mt: 2, color: "#666" }}>
                    Can't scan? Enter this code manually: {totpSetup.sharedSecret}
                  </Typography>
                </Box>
              )}

              <Box component="form" onSubmit={handleTotpSetup} sx={{ width: "100%" }}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  label="Enter 6-digit code from your app"
                  placeholder="123456"
                  value={mfaData.code}
                  onChange={(e) => setMfaData({ ...mfaData, code: e.target.value })}
                  sx={{ background: "#fafbfc", borderRadius: 1 }}
                  inputProps={{ maxLength: 6, pattern: "[0-9]*" }}
                />
                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  disabled={isLoading || mfaData.code.length !== 6}
                  sx={{
                    mt: 3,
                    mb: 2,
                    fontWeight: 700,
                    background: "linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)",
                    color: "#fff",
                    "&:hover": {
                      opacity: 0.95,
                    },
                  }}
                >
                  {isLoading ? "Setting up..." : "Complete Setup"}
                </Button>
              </Box>
            </Box>
          ) : !mfaRequired ? (
            <Box component="form" onSubmit={handleLogin} sx={{ mt: 2, width: "100%" }}>
              <TextField
                margin="normal"
                required
                fullWidth
                label="Username or Email"
                value={loginData.username}
                onChange={(e) => setLoginData({ ...loginData, username: e.target.value })}
                sx={{ background: "#fafbfc", borderRadius: 1 }}
              />
              <TextField
                margin="normal"
                required
                fullWidth
                type="password"
                label="Password"
                value={loginData.password}
                onChange={(e) => setLoginData({ ...loginData, password: e.target.value })}
                sx={{ background: "#fafbfc", borderRadius: 1 }}
              />
              <Button
                type="submit"
                fullWidth
                variant="contained"
                disabled={isLoading}
                sx={{
                  mt: 3,
                  mb: 2,
                  fontWeight: 700,
                  background: "linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)",
                  color: "#fff",
                  "&:hover": {
                    opacity: 0.95,
                  },
                }}
              >
                {isLoading ? "Signing in..." : "Sign in"}
              </Button>
            </Box>
          ) : (
            <Box component="form" onSubmit={handleConfirmMFA} sx={{ mt: 2, width: "100%" }}>
              <TextField
                margin="normal"
                required
                fullWidth
                label="Verification Code"
                placeholder="Enter 6-digit code"
                value={mfaData.code}
                onChange={(e) => setMfaData({ ...mfaData, code: e.target.value })}
                sx={{ background: "#fafbfc", borderRadius: 1 }}
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={mfaData.rememberDevice}
                    onChange={(e) =>
                      setMfaData({ ...mfaData, rememberDevice: e.target.checked })
                    }
                    color="primary"
                  />
                }
                label="Remember this device for 30 days"
              />
              <Button
                type="submit"
                fullWidth
                variant="contained"
                disabled={isLoading}
                sx={{
                  mt: 3,
                  mb: 2,
                  fontWeight: 700,
                  background: "linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)",
                  color: "#fff",
                  "&:hover": {
                    opacity: 0.95,
                  },
                }}
              >
                {isLoading ? "Verifying..." : "Verify"}
              </Button>
            </Box>
          )}
        </Paper>
      </Container>
    </Box>
  );
}

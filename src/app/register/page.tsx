'use client';

import Link from '@/components/Link';
import { setAuthToken } from '@/utils/auth';
import {
    Box,
    Button,
    Container,
    Paper,
    Stack,
    TextField,
    Typography
} from '@mui/material';
import { useFormik } from 'formik';
import { useRouter } from 'next/navigation';
import * as Yup from 'yup';
import Image from 'next/image';



const Register = () => {
    const router = useRouter();

    const validationSchema = Yup.object({

        firstName: Yup.string().required('First name is required')
            .max(255, 'Must be 255 characters or less'),

        lastName: Yup.string().required('Last name is required')
            .max(255, 'Must be 255 characters or less'),

        loginEmail: Yup.string().required('Email is required')
            .email('Invalid email address')
            .max(255, 'Must be 255 characters or less'),

        personalBusinessEmail: Yup.string()
            .email('Invalid email address')
            .max(255, 'Must be 255 characters or less'),

        phone: Yup.string().required('Phone number is required')
            .max(255, 'Must be 255 characters or less'),

        professionalTitle: Yup.string()
            .max(255, 'Must be 255 characters or less'),

        password: Yup.string().required('Password is required')
            .min(8, 'Must be at least 8 characters')
            .max(255, 'Must be 255 characters or less'),
        confirmPassword: Yup.string().required('Confirm Password is required')
            .min(8, 'Must be at least 8 characters')
            .max(255, 'Must be 255 characters or less')
            .oneOf([Yup.ref('password')], 'Passwords must match')

    });
    const initialValues = {
        firstName: '',
        lastName: '',
        loginEmail: '',
        auth0Id: 'abc123',
        password: '',
        confirmPassword: '',
        phone: '',
        professionalTitle: '',
        personalBusinessEmail: '',
        membershipTier: 'basic',
        communityStatus: 'unverified',
        hasSeenFirstLoginMessage: false,
    };

    const formik = useFormik({
        initialValues,
        validationSchema,
        onSubmit: async (values) => {
            const { confirmPassword, ...rest } = values;

            try {
                const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/members/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(rest),
                });

                const data = await response.json();

                if (data.success) {
                    // Handle successful registration
                    console.log('Registration successful:', data);

                    // If registration returns a token, store it and redirect to dashboard
                    if (data.token) {
                        setAuthToken(data.token);
                        router.push('/dashboard');
                    } else {
                        // If no token, redirect to login page
                        router.push('/');
                    }
                } else {
                    // Handle registration error
                    console.error('Registration error:', data.message);
                    // You can add toast notification or error state here
                }
            } catch (error) {
                console.error('Error during registration:', error);
                // You can add toast notification or error state here
            }
        },
    });

    const { dirty, isValid, handleSubmit, handleBlur, handleChange, values, errors, touched, handleReset } = formik;

    return (
        <Box
            sx={{
                height: '100vh',
                width: '100vw',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: 'linear-gradient(135deg, #1e3a8a 0%, #f7c873 100%)',
                overflow: 'hidden',
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
            }}
        >
            <Container maxWidth="sm" disableGutters>
                <Paper
                    elevation={6}
                    sx={{
                        p: { xs: 3, sm: 5 },
                        borderRadius: 4,
                        boxShadow: '0 8px 32px 0 rgba(30,58,138,0.2)',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        width: { xs: '90vw', sm: 500 },
                        maxWidth: 520,
                        mx: 'auto',
                        maxHeight: '90vh',
                        overflowY: 'auto'
                    }}
                >
                    <Box sx={{ mb: 3, display: 'flex', justifyContent: 'center' }}>
                        <Image
                            src="/CO-gold.svg"
                            alt="US Chamber CO Logo"
                            width={120}
                            height={56}
                            style={{ objectFit: 'contain' }}
                        />
                    </Box>

                    <Typography
                        variant="h4"
                        component="h1"
                        gutterBottom
                        align="center"
                        sx={{ fontWeight: 700, color: '#1e3a8a' }}
                    >
                        Welcome to CO
                    </Typography>

                    <Typography
                        variant="subtitle1"
                        align="center"
                        sx={{ mb: 2, color: '#555' }}
                    >
                        Create Your Account
                    </Typography>

                    <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2, width: '100%' }}>
                        <Stack spacing={2}>
                            <Box sx={{ display: 'flex', gap: 2 }}>
                                <TextField
                                    fullWidth
                                    name="firstName"
                                    label="First name"
                                    placeholder="Enter your first name"
                                    value={values.firstName}
                                    onChange={handleChange}
                                    onBlur={handleBlur}
                                    error={touched.firstName && Boolean(errors.firstName)}
                                    helperText={touched.firstName && errors.firstName}
                                    sx={{ background: '#fafbfc', borderRadius: 1 }}
                                    autoFocus
                                />
                                <TextField
                                    fullWidth
                                    name="lastName"
                                    label="Last name"
                                    placeholder="Enter your last name"
                                    value={values.lastName}
                                    onChange={handleChange}
                                    onBlur={handleBlur}
                                    error={touched.lastName && Boolean(errors.lastName)}
                                    helperText={touched.lastName && errors.lastName}
                                    sx={{ background: '#fafbfc', borderRadius: 1 }}
                                />
                            </Box>

                            <TextField
                                fullWidth
                                name="loginEmail"
                                label="Email"
                                placeholder="Enter your email"
                                type="email"
                                value={values.loginEmail}
                                onChange={handleChange}
                                onBlur={handleBlur}
                                error={touched.loginEmail && Boolean(errors.loginEmail)}
                                helperText={touched.loginEmail && errors.loginEmail}
                                sx={{ background: '#fafbfc', borderRadius: 1 }}
                            />

                            <TextField
                                fullWidth
                                name="password"
                                label="Password"
                                placeholder="Enter your password"
                                type="password"
                                value={values.password}
                                onChange={handleChange}
                                onBlur={handleBlur}
                                error={touched.password && Boolean(errors.password)}
                                helperText={touched.password && errors.password}
                                sx={{ background: '#fafbfc', borderRadius: 1 }}
                            />

                            <TextField
                                fullWidth
                                name="confirmPassword"
                                label="Confirm Password"
                                placeholder="Enter your confirm password"
                                type="password"
                                value={values.confirmPassword}
                                onChange={handleChange}
                                onBlur={handleBlur}
                                error={touched.confirmPassword && Boolean(errors.confirmPassword)}
                                helperText={touched.confirmPassword && errors.confirmPassword}
                                sx={{ background: '#fafbfc', borderRadius: 1 }}
                            />

                            <TextField
                                fullWidth
                                name="professionalTitle"
                                label="Professional title"
                                placeholder="Enter your professional title"
                                value={values.professionalTitle}
                                onChange={handleChange}
                                onBlur={handleBlur}
                                error={touched.professionalTitle && Boolean(errors.professionalTitle)}
                                helperText={touched.professionalTitle && errors.professionalTitle}
                                sx={{ background: '#fafbfc', borderRadius: 1 }}
                            />

                            <TextField
                                fullWidth
                                name="phone"
                                label="Phone number"
                                placeholder="Enter your phone number"
                                type="tel"
                                value={values.phone}
                                onChange={handleChange}
                                onBlur={handleBlur}
                                error={touched.phone && Boolean(errors.phone)}
                                helperText={touched.phone && errors.phone}
                                sx={{ background: '#fafbfc', borderRadius: 1 }}
                            />

                            <TextField
                                fullWidth
                                name="personalBusinessEmail"
                                label="Personal/Business Email (Optional)"
                                placeholder="Enter your personal or business email"
                                type="email"
                                value={values.personalBusinessEmail}
                                onChange={handleChange}
                                onBlur={handleBlur}
                                error={touched.personalBusinessEmail && Boolean(errors.personalBusinessEmail)}
                                helperText={touched.personalBusinessEmail && errors.personalBusinessEmail}
                                sx={{ background: '#fafbfc', borderRadius: 1 }}
                            />

                            <Button
                                type="submit"
                                fullWidth
                                variant="contained"
                                disabled={!isValid || !dirty}
                                sx={{
                                    mt: 3,
                                    mb: 2,
                                    fontWeight: 700,
                                    background: 'linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)',
                                    color: '#fff',
                                    boxShadow: '0 2px 8px 0 rgba(30,58,138,0.10)',
                                    '&:hover': {
                                        background: 'linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)',
                                        opacity: 0.95,
                                    },
                                    '&:disabled': {
                                        opacity: 0.6,
                                    },
                                }}
                            >
                                Register
                            </Button>

                            <Typography
                                align="center"
                                sx={{
                                    color: '#555',
                                    fontSize: '14px',
                                    mt: 1
                                }}
                            >
                                <Link
                                    href="/"
                                    sx={{
                                        color: '#1e3a8a',
                                        textDecoration: 'none',
                                        '&:hover': {
                                            textDecoration: 'underline'
                                        }
                                    }}
                                >
                                    Already have an account? Sign in
                                </Link>
                            </Typography>
                        </Stack>
                    </Box>
                </Paper>
            </Container>
        </Box>
    );
};

export default Register;
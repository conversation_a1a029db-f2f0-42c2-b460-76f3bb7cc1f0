'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Chip,
  Alert,


  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Delete as DeleteIcon,

  FilterList as FilterIcon,
  Refresh as RefreshIcon,

  Edit as EditIcon,
  CheckCircle as CheckCircleIcon,
  Block as BlockIcon,
} from '@mui/icons-material';
import { AdminUserTable } from '@/components/admin/AdminUserTable';
import { AdminUserForm } from '@/components/admin/AdminUserForm';
import { ConfirmDialog } from '@/components/common/ConfirmDialog';
import { RoleGuard } from '@/components/auth/RoleGuard';
import { PageHeader } from '@/components/common/PageHeader';
import { adminUsersService } from '@/services/adminUsers';
import {
  AdminUser,
  AdminUserFilters,
  AdminUserSearchParams,
  AdminUserStats,
} from '@/types/adminUser';
import AppLayout from '@/layout/AppLayout';

export default function AdminUsersPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [adminUsers, setAdminUsers] = useState<AdminUser[]>([]);
  const [stats, setStats] = useState<AdminUserStats | null>(null);
  const [departments, setDepartments] = useState<string[]>([]);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [adminUserToDelete, setAdminUserToDelete] = useState<AdminUser | null>(null);
  const [adminUserToEdit, setAdminUserToEdit] = useState<AdminUser | null>(null);

  // Mock current user role - in real app, this would come from auth context
  const currentUserRole = 'super_admin';

  // Table state
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortBy, setSortBy] = useState<string>('firstName');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [total, setTotal] = useState(0);

  // Filter state
  const [filters, setFilters] = useState<AdminUserFilters>({
    search: '',
    status: 'all',
    role: 'all',
    department: '',
  });

  useEffect(() => {
    fetchData();
  }, [page, pageSize, sortBy, sortOrder, filters]);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      const searchParams: AdminUserSearchParams = {
        page,
        pageSize,
        sortBy,
        sortOrder,
        filters,
      };

      const [usersResponse, statsData, departmentsData] = await Promise.all([
        adminUsersService.getAdminUsers(searchParams),
        adminUsersService.getAdminUserStats(),
        adminUsersService.getDepartments(),
      ]);

      setAdminUsers(usersResponse.adminUsers);
      setTotal(usersResponse.total);
      setStats(statsData);
      setDepartments(departmentsData);
    } catch (err: any) {
      console.error('Admin users data fetch error:', err);
      setError(err.message || 'Failed to load admin users data');
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prev => ({ ...prev, search: event.target.value }));
    setPage(1); // Reset to first page when searching
  };

  const handleFilterChange = (key: keyof AdminUserFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPage(1); // Reset to first page when filtering
  };

  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setPage(1);
  };

  const handleSelectionChange = (newSelectedIds: string[]) => {
    setSelectedIds(newSelectedIds);
  };

  const handleEdit = (adminUser: AdminUser) => {
    setAdminUserToEdit(adminUser);
    setShowEditDialog(true);
  };

  const handleDelete = (adminUser: AdminUser) => {
    setAdminUserToDelete(adminUser);
    setShowDeleteDialog(true);
  };

  const handleView = (adminUser: AdminUser) => {
    // TODO: Implement view functionality
    console.log('View admin user:', adminUser);
  };

  const handleStatusChange = async (adminUser: AdminUser, status: 'active' | 'inactive' | 'pending') => {
    try {
      await adminUsersService.updateAdminUser(adminUser.id, { id: adminUser.id, status });
      fetchData();
    } catch (err: any) {
      setError('Failed to update user status');
    }
  };

  const handleRoleChange = async (adminUser: AdminUser, role: 'super_admin' | 'admin' | 'moderator') => {
    try {
      await adminUsersService.updateAdminUser(adminUser.id, { id: adminUser.id, role });
      fetchData();
    } catch (err: any) {
      setError('Failed to update user role');
    }
  };

  const handleBulkDelete = async () => {
    try {
      await adminUsersService.bulkAction({
        adminUserIds: selectedIds,
        action: 'delete',
      });
      setSelectedIds([]);
      fetchData();
    } catch (err: any) {
      setError('Failed to delete selected users');
    }
  };

  const handleBulkActivate = async () => {
    try {
      await adminUsersService.bulkAction({
        adminUserIds: selectedIds,
        action: 'activate',
      });
      setSelectedIds([]);
      fetchData();
    } catch (err: any) {
      setError('Failed to activate selected users');
    }
  };

  const handleBulkDeactivate = async () => {
    try {
      await adminUsersService.bulkAction({
        adminUserIds: selectedIds,
        action: 'deactivate',
      });
      setSelectedIds([]);
      fetchData();
    } catch (err: any) {
      setError('Failed to deactivate selected users');
    }
  };

  const handleAddAdminUser = () => {
    setShowAddDialog(true);
  };

  const handleCreateAdminUser = async (data: any) => {
    try {
      await adminUsersService.createAdminUser(data);
      setShowAddDialog(false);
      fetchData();
    } catch (err: any) {
      throw err; // Let the form handle the error
    }
  };

  const handleUpdateAdminUser = async (data: any) => {
    if (!adminUserToEdit) return;
    
    try {
      await adminUsersService.updateAdminUser(adminUserToEdit.id, data);
      setShowEditDialog(false);
      setAdminUserToEdit(null);
      fetchData();
    } catch (err: any) {
      throw err; // Let the form handle the error
    }
  };

  const confirmDelete = async () => {
    if (!adminUserToDelete) return;

    try {
      await adminUsersService.deleteAdminUser(adminUserToDelete.id);
      setShowDeleteDialog(false);
      setAdminUserToDelete(null);
      fetchData();
    } catch (err: any) {
      setError('Failed to delete admin user');
    }
  };

  // Check if current user has permission to manage admin users
  const canManageAdminUsers = currentUserRole === 'super_admin';

  return (
    <RoleGuard requiredRole="super_admin" currentUserRole={currentUserRole}>
      <AppLayout>
        <Container maxWidth="xl">
          <Box sx={{ py: 3 }}>
            {/* Page Header */}
            <PageHeader
              title="Admin User Management"
              subtitle="Manage system administrators and their permissions"
              breadcrumbs={[
                { label: 'Dashboard', href: '/dashboard' },
                { label: 'Admin Users' }
              ]}
              actions={
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddAdminUser}
                  sx={{ minWidth: 150 }}
                >
                  Add Admin User
                </Button>
              }
            />

            {error && (
              <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                {error}
              </Alert>
            )}

            {/* Statistics Cards */}
            {stats && (
              <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid item xs={12} sm={6} md={2}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
                        {stats.total}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Users
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} sm={6} md={2}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" sx={{ fontWeight: 600, color: 'info.main' }}>
                        {stats.superAdmins}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Super Admins
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={2}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" sx={{ fontWeight: 600, color: 'secondary.main' }}>
                        {stats.newThisMonth}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        New This Month
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            )}

            {/* Search and Filters */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      placeholder="Search admin users..."
                      value={filters.search}
                      onChange={handleSearchChange}
                      InputProps={{
                        startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={8}>
                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                      <FormControl size="small" sx={{ minWidth: 120 }}>
                        <InputLabel>Role</InputLabel>
                        <Select
                          value={filters.role}
                          label="Role"
                          onChange={(e) => handleFilterChange('role', e.target.value)}
                        >
                          <MenuItem value="all">All Roles</MenuItem>
                          <MenuItem value="super_admin">Super Admin</MenuItem>
                          <MenuItem value="admin">Admin</MenuItem>
                          <MenuItem value="moderator">Moderator</MenuItem>
                        </Select>
                      </FormControl>
                      {selectedIds.length > 0 && (
                        <Box sx={{ display: 'flex', gap: 1, ml: 'auto' }}>
                          <Chip label={`${selectedIds.length} selected`} color="primary" />
                          <Tooltip title="Activate Selected">
                            <IconButton
                              color="success"
                              onClick={handleBulkActivate}
                              size="small"
                            >
                              <CheckCircleIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Deactivate Selected">
                            <IconButton
                              color="warning"
                              onClick={handleBulkDeactivate}
                              size="small"
                            >
                              <BlockIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete Selected">
                            <IconButton
                              color="error"
                              onClick={handleBulkDelete}
                              size="small"
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      )}
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>

            {/* Admin Users Table */}
            <AdminUserTable
              adminUsers={adminUsers}
              loading={loading}
              selectedIds={selectedIds}
              onSelectionChange={handleSelectionChange}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onView={handleView}
              onStatusChange={handleStatusChange}
              onRoleChange={handleRoleChange}
              sortBy={sortBy}
              sortOrder={sortOrder}
              onSortChange={handleSortChange}
              page={page}
              pageSize={pageSize}
              total={total}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              currentUserRole={currentUserRole}
            />

            {/* Delete Confirmation Dialog */}
            <ConfirmDialog
              open={showDeleteDialog}
              title="Delete Admin User"
              message={`Are you sure you want to delete ${adminUserToDelete?.firstName} ${adminUserToDelete?.lastName}? This action cannot be undone.`}
              onConfirm={confirmDelete}
              onClose={() => {
                setShowDeleteDialog(false);
                setAdminUserToDelete(null);
              }}
            />

            {/* Add Admin User Dialog */}
            <Dialog
              open={showAddDialog}
              onClose={() => setShowAddDialog(false)}
              maxWidth="md"
              fullWidth
              PaperProps={{
                sx: {
                  minHeight: '80vh',
                  maxHeight: '90vh',
                },
              }}
            >
              <DialogTitle sx={{ pb: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <AddIcon color="primary" />
                  Add New Admin User
                </Box>
              </DialogTitle>
              <DialogContent sx={{ pb: 0 }}>
                <AdminUserForm
                  currentUserRole={currentUserRole}
                  onSubmit={handleCreateAdminUser}
                  onCancel={() => setShowAddDialog(false)}
                />
              </DialogContent>

            </Dialog>

            {/* Edit Admin User Dialog */}
            {adminUserToEdit && (
              <Dialog
                open={showEditDialog}
                onClose={() => {
                  setShowEditDialog(false);
                  setAdminUserToEdit(null);
                }}
                maxWidth="md"
                fullWidth
                PaperProps={{
                  sx: {
                    minHeight: '80vh',
                    maxHeight: '90vh',
                  },
                }}
              >
                <DialogTitle sx={{ pb: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <EditIcon color="primary" />
                    Edit Admin User: {adminUserToEdit.firstName} {adminUserToEdit.lastName}
                  </Box>
                </DialogTitle>
                <DialogContent sx={{ pb: 0 }}>
                  <AdminUserForm
                    adminUser={adminUserToEdit}
                    currentUserRole={currentUserRole}
                    onSubmit={handleUpdateAdminUser}
                    onCancel={() => {
                      setShowEditDialog(false);
                      setAdminUserToEdit(null);
                    }}
                  />
                </DialogContent>
                <DialogActions sx={{ p: 3, pt: 1 }}>
                  <Button
                    onClick={() => {
                      setShowEditDialog(false);
                      setAdminUserToEdit(null);
                    }}
                    variant="outlined"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    form="admin-user-form"
                    sx={{ minWidth: 120 }}
                  >
                    Update User
                  </Button>
                </DialogActions>
              </Dialog>
            )}
          </Box>
        </Container>
      </AppLayout>
    </RoleGuard>
  );
} 
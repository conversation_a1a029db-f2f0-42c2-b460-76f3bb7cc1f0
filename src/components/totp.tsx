import { confirmSignIn } from '@aws-amplify/auth';
import QRCode from 'qrcode.react';
import { useState } from 'react';
import { Box, Typography, TextField, Button } from '@mui/material';

function TOTPSetup({ username, sharedSecret }: { username: string; sharedSecret: string }) {
  const [code, setCode] = useState('');

  const totpUri = `otpauth://totp/MyApp:${username}?secret=${sharedSecret}&issuer=MyApp`;

  const handleVerify = async () => {
    try {
      await confirmSignIn({ challengeResponse: code });
      alert('✅ TOTP verified!');
    } catch (err) {
      console.error(err);
      alert('❌ Invalid code');
    }
  };

  return (
    <Box sx={{ mt: 2 }}>
      <Typography>Scan this QR Code with your Authenticator App:</Typography>
      <QRCode value={totpUri} size={200} />
      <TextField
        label="Enter 6-digit code"
        value={code}
        onChange={(e) => setCode(e.target.value)}
        sx={{ mt: 2 }}
      />
      <Button onClick={handleVerify} sx={{ mt: 2 }}>
        Verify
      </Button>
    </Box>
  );
}

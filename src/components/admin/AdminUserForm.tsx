'use client';

import React, { useState } from 'react';
import {
  Box,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  useTheme,
  Alert,
  Button,
  CircularProgress,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Person as PersonIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { AdminUser, ADMIN_PERMISSIONS } from '@/types/adminUser';
import { createAdminUser, CreateAdminUserRequest } from '@/services/adminAPI';
import { showToast } from '@/utils/toast';

// Validation schemas
const createAdminUserSchema = z.object({
  username: z
    .string()
    .min(1, 'Username is required')
    .min(3, 'Username must be at least 3 characters')
    .max(50, 'Username must be less than 50 characters'),

  firstName: z
    .string()
    .min(1, 'First name is required')
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters'),

  lastName: z
    .string()
    .min(1, 'Last name is required')
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters'),

  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .max(100, 'Email must be less than 100 characters'),

  role: z.enum(['super_admin', 'admin', 'moderator'], {
    errorMap: () => ({ message: 'Please select a valid role' }),
  }),

  temporaryPassword: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(100, 'Password must be less than 100 characters'),
});

const updateAdminUserSchema = createAdminUserSchema.extend({
  id: z.string().min(1, 'User ID is required'),
  status: z.enum(['active', 'inactive', 'pending'], {
    errorMap: () => ({ message: 'Please select a valid status' }),
  }),
});

const DEPARTMENTS = [
  'IT',
  'Operations',
  'Member Services',
  'Marketing',
  'Analytics',
  'Finance',
  'Legal',
  'Human Resources',
  'Communications',
  'Other'
];

interface AdminUserFormProps {
  adminUser?: AdminUser; // If provided, form is in edit mode
  currentUserRole?: string; // Current user's role for authorization
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  error?: string | null;
}

export function AdminUserForm({
  adminUser,
  currentUserRole = 'admin',
  onSubmit,
  onCancel,
  loading = false,
  error
}: AdminUserFormProps) {
  const theme = useTheme();
  const [profileImage, setProfileImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(
    adminUser?.profileImage || null
  );

  const isEditMode = !!adminUser;

  // Check if current user can manage the target user
  const canManageUser = (targetRole: string) => {
    const roleHierarchy = {
      super_admin: 3,
      admin: 2,
      moderator: 1
    };
    return roleHierarchy[currentUserRole as keyof typeof roleHierarchy] > roleHierarchy[targetRole as keyof typeof roleHierarchy];
  };
  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm({
    resolver: zodResolver(isEditMode ? updateAdminUserSchema : createAdminUserSchema),
    defaultValues: (isEditMode ? {
      id: adminUser!.id,
      username: adminUser!.username,
      firstName: adminUser!.firstName,
      lastName: adminUser!.lastName,
      email: adminUser!.email,
      role: adminUser!.role,
      temporaryPassword: '',
    } : {
      username: '',
      firstName: '',
      lastName: '',
      email: '',
      role: 'moderator' as const,
      temporaryPassword: '',
    }) as any,
    mode: 'onChange',
  });

  const watchedRole = watch('role');

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setProfileImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImage = () => {
    setProfileImage(null);
    setImagePreview(null);
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const watchedFirstName = watch('firstName');
  const watchedLastName = watch('lastName');

  const handleFormSubmit = async (data: any) => {
    console.log('first')
    try {
      if (!isEditMode) {
        // Create new admin user via API
        const loadingToast = showToast.loading('Creating admin user...');

        const createRequest: CreateAdminUserRequest = {
          email: data.email,
          username: data.username,
          password: data.temporaryPassword,
          role: data.role,
          // firstName: data.firstName,
          // lastName: data.lastName,
        };

        const result = await createAdminUser(createRequest);
        showToast.dismiss(loadingToast);

        if (result.success) {
          showToast.success('Admin user created successfully!');
          reset();
          setProfileImage(null);
          setImagePreview(null);
          // Call parent onSubmit if provided
          if (onSubmit) {
            await onSubmit(result.data);
          }
        } else {
          showToast.error(result.error || 'Failed to create admin user');
        }
      } else {
        // Update existing user (call parent onSubmit)
        await onSubmit(data);
      }
    } catch (err: any) {
      console.error('Form submission error:', err);
      showToast.error(err.message || 'An error occurred');
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'error';
      case 'admin':
        return 'warning';
      case 'moderator':
        return 'info';
      default:
        return 'default';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'error';
      case 'pending':
        return 'warning';
      default:
        return 'default';
    }
  };

  return (
    <Box component="form" id="admin-user-form" onSubmit={handleSubmit(handleFormSubmit)} sx={{ width: '100%' }}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Basic Information */}
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <PersonIcon />
        Basic Information
      </Typography>
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6}>
          <Controller
            name="username"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Username"
                error={!!errors.username}
                helperText={errors.username?.message as string || ''}
                disabled={loading}
              />
            )}
          />
        </Grid>
        {/* <Grid item xs={12} sm={6}>
          <Controller
            name="firstName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="First Name"
                error={!!errors.firstName}
                helperText={errors.firstName?.message as string || ''}
                disabled={loading}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <Controller
            name="lastName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Last Name"
                error={!!errors.lastName}
                helperText={errors.lastName?.message as string || ''}
                disabled={loading}
              />
            )}
          />
        </Grid> */}
        <Grid item xs={12} sm={6}>
          <Controller
            name="email"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Email"
                type="email"
                error={!!errors.email}
                helperText={errors.email?.message as string || ''}
                disabled={loading}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <Controller
            name="temporaryPassword"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Temporary Password"
                type="password"
                error={!!errors.temporaryPassword}
                helperText={errors.temporaryPassword?.message as string || ''}
                disabled={loading}
              />
            )}
          />
        </Grid>
      </Grid>

      {/* Role and Status */}
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <SecurityIcon />
        Role & Status
      </Typography>
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6}>
          <Controller
            name="role"
            control={control}
            render={({ field }) => (
              <FormControl fullWidth error={!!errors.role}>
                <InputLabel>Role</InputLabel>
                <Select {...field} label="Role" disabled={loading}>
                  <MenuItem value="moderator" disabled={!canManageUser('moderator')}>
                    Moderator
                  </MenuItem>
                  <MenuItem value="admin" disabled={!canManageUser('admin')}>
                    Admin
                  </MenuItem>
                  <MenuItem value="super_admin" disabled={!canManageUser('super_admin')}>
                    Super Admin
                  </MenuItem>
                </Select>
                {errors.role && (
                  <Typography variant="caption" color="error" sx={{ mt: 0.5, display: 'block' }}>
                    {errors.role.message as string}
                  </Typography>
                )}
              </FormControl>
            )}
          />
        </Grid>
        {isEditMode && (
          <Grid item xs={12} sm={6}>
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <FormControl fullWidth error={!!(errors as any).status}>
                  <InputLabel>Status</InputLabel>
                  <Select {...field} label="Status" disabled={loading}>
                    <MenuItem value="active">Active</MenuItem>
                    <MenuItem value="inactive">Inactive</MenuItem>
                    <MenuItem value="pending">Pending</MenuItem>
                  </Select>
                  {(errors as any).status && (
                    <Typography variant="caption" color="error" sx={{ mt: 0.5, display: 'block' }}>
                      {(errors as any).status.message}
                    </Typography>
                  )}
                </FormControl>
              )}
            />
          </Grid>
        )}
      </Grid>

      {/* Form Actions */}
      <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 4 }}>
        <Button
          variant="outlined"
          onClick={onCancel}
          disabled={loading}
          startIcon={<CancelIcon />}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="contained"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
          sx={{
            background: 'linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)',
            color: '#fff',
            '&:hover': {
              opacity: 0.95,
            },
          }}
        >
          {loading ? 'Processing...' : isEditMode ? 'Update User' : 'Create User'}
        </Button>
      </Box>
    </Box>
  );
}
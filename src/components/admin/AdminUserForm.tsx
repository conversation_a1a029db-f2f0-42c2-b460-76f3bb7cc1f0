'use client';

import React, { useState } from 'react';
import {
  Box,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  useTheme,
  Alert,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { AdminUser, ADMIN_PERMISSIONS } from '@/types/adminUser';

// Validation schemas
const createAdminUserSchema = z.object({
  firstName: z
    .string()
    .min(1, 'First name is required')
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters'),
  
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .max(100, 'Email must be less than 100 characters'),
  
  role: z.enum(['super_admin', 'admin', 'moderator'], {
    errorMap: () => ({ message: 'Please select a valid role' }),
  }),
  
  temporaryPassword: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(100, 'Password must be less than 100 characters'),
});

const updateAdminUserSchema = createAdminUserSchema.extend({
  id: z.string().min(1, 'User ID is required'),
  status: z.enum(['active', 'inactive', 'pending'], {
    errorMap: () => ({ message: 'Please select a valid status' }),
  }),
});

const DEPARTMENTS = [
  'IT',
  'Operations',
  'Member Services',
  'Marketing',
  'Analytics',
  'Finance',
  'Legal',
  'Human Resources',
  'Communications',
  'Other'
];

interface AdminUserFormProps {
  adminUser?: AdminUser; // If provided, form is in edit mode
  currentUserRole?: string; // Current user's role for authorization
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  error?: string | null;
}

export function AdminUserForm({ 
  adminUser, 
  currentUserRole = 'admin',
  onSubmit, 
  onCancel, 
  loading = false, 
  error 
}: AdminUserFormProps) {
  const theme = useTheme();
  const [profileImage, setProfileImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(
    adminUser?.profileImage || null
  );

  const isEditMode = !!adminUser;

  // Check if current user can manage the target user
  const canManageUser = (targetRole: string) => {
    const roleHierarchy = {
      super_admin: 3,
      admin: 2,
      moderator: 1
    };
    return roleHierarchy[currentUserRole as keyof typeof roleHierarchy] > roleHierarchy[targetRole as keyof typeof roleHierarchy];
  };
  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm({
    resolver: zodResolver(isEditMode ? updateAdminUserSchema : createAdminUserSchema),
    defaultValues: (isEditMode ? {
      id: adminUser!.id,
      username: adminUser!.username,
      email: adminUser!.email,
      role: adminUser!.role,
      temporaryPassword: '',
    } : {
      username: '',
      email: '',
      role: 'moderator' as const,
      temporaryPassword: '',
    }) as any,
    mode: 'onChange',
  });

  const watchedRole = watch('role');

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setProfileImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImage = () => {
    setProfileImage(null);
    setImagePreview(null);
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const watchedFirstName = watch('firstName');
  const watchedLastName = watch('lastName');

  const handleFormSubmit = async (data: any) => {
    try {
      await onSubmit(data);
      if (!isEditMode) {
        reset();
        setProfileImage(null);
        setImagePreview(null);
      }
    } catch (err) {
      // Error handling is done by the parent component
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'error';
      case 'admin':
        return 'warning';
      case 'moderator':
        return 'info';
      default:
        return 'default';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'error';
      case 'pending':
        return 'warning';
      default:
        return 'default';
    }
  };

  return (
    <Box component="form" id="admin-user-form" onSubmit={handleSubmit(handleFormSubmit)} sx={{ width: '100%' }}>      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}{/* Basic Information */}
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <PersonIcon />
        Basic Information
      </Typography>
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6}>
          <Controller
            name="username"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Username"
                error={!!errors.username}
                helperText={errors.username?.message || ''}
                disabled={loading}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <Controller
            name="email"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Email"
                type="email"
                error={!!errors.email}
                helperText={errors.email?.message || ''}
                disabled={loading}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <Controller
            name="temporaryPassword"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Temporary Password"
                type="password"
                error={!!errors.temporaryPassword}
                helperText={errors.temporaryPassword?.message || ''}
                disabled={loading}
              />
            )}
          />
        </Grid>
      </Grid>      {/* Role and Status */}
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <SecurityIcon />
        Role & Status
      </Typography>
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6}>
          <Controller
            name="role"
            control={control}
            render={({ field }) => (
              <FormControl fullWidth error={!!errors.role}>
                <InputLabel>Role</InputLabel>
                <Select {...field} label="Role" disabled={loading}>
                  <MenuItem value="moderator" disabled={!canManageUser('moderator')}>
                    Moderator
                  </MenuItem>
                  <MenuItem value="admin" disabled={!canManageUser('admin')}>
                    Admin
                  </MenuItem>
                  <MenuItem value="super_admin" disabled={!canManageUser('super_admin')}>
                    Super Admin
                  </MenuItem>
                </Select>
                {errors.role && (
                  <Typography variant="caption" color="error" sx={{ mt: 0.5, display: 'block' }}>
                    {errors.role.message}
                  </Typography>
                )}
              </FormControl>
            )}
          />
        </Grid>
        {isEditMode && (
          <Grid item xs={12} sm={6}>
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <FormControl fullWidth error={!!(errors as any).status}>
                  <InputLabel>Status</InputLabel>
                  <Select {...field} label="Status" disabled={loading}>
                    <MenuItem value="active">Active</MenuItem>
                    <MenuItem value="inactive">Inactive</MenuItem>
                    <MenuItem value="pending">Pending</MenuItem>
                  </Select>
                  {(errors as any).status && (
                    <Typography variant="caption" color="error" sx={{ mt: 0.5, display: 'block' }}>
                      {(errors as any).status.message}
                    </Typography>
                  )}
                </FormControl>
              )}
            />
          </Grid>
        )}
      </Grid>
    </Box>
  );
}
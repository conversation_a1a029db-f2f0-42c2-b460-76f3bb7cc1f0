export interface AdminUser {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: 'super_admin' | 'admin' | 'moderator';
  status: 'active' | 'inactive' | 'pending';
  permissions: string[];
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  createdBy?: string;
  profileImage?: string;
  phone?: string;
  department?: string;
  notes?: string;
}

export interface AdminUserFilters {
  search?: string;
  status?: 'all' | 'active' | 'inactive' | 'pending';
  role?: 'all' | 'super_admin' | 'admin' | 'moderator';
  department?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface AdminUserSearchParams {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters: AdminUserFilters;
}

export interface AdminUserListResponse {
  adminUsers: AdminUser[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface CreateAdminUserRequest {
  firstName: string;
  lastName: string;
  email: string;
  role: 'super_admin' | 'admin' | 'moderator';
  phone?: string;
  department?: string;
  notes?: string;
  generatePassword?: boolean;
  sendInvitation?: boolean;
}

export interface UpdateAdminUserRequest extends Partial<CreateAdminUserRequest> {
  id: string;
  status?: 'active' | 'inactive' | 'pending';
}

export interface BulkActionRequest {
  adminUserIds: string[];
  action: 'delete' | 'activate' | 'deactivate' | 'change_role';
  role?: 'super_admin' | 'admin' | 'moderator';
}

export interface AdminUserStats {
  total: number;
  active: number;
  inactive: number;
  pending: number;
  superAdmins: number;
  admins: number;
  moderators: number;
  newThisMonth: number;
}

// Column definitions for the table
export interface AdminUserTableColumn {
  id: keyof AdminUser;
  label: string;
  sortable: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
}

export const ADMIN_USER_TABLE_COLUMNS: AdminUserTableColumn[] = [
  { id: 'firstName', label: 'Name', sortable: true, width: '200px' },
  { id: 'email', label: 'Email', sortable: true, width: '250px' },
  { id: 'role', label: 'Role', sortable: true, width: '120px', align: 'center' },
  { id: 'department', label: 'Department', sortable: true, width: '150px' },
  { id: 'status', label: 'Status', sortable: true, width: '100px', align: 'center' },
  { id: 'createdAt', label: 'Created', sortable: true, width: '120px', align: 'center' },
  { id: 'lastLoginAt', label: 'Last Login', sortable: true, width: '120px', align: 'center' },
];

// Permission definitions
export const ADMIN_PERMISSIONS = {
  SUPER_ADMIN: [
    'manage_admin_users',
    'manage_members',
    'manage_analytics',
    'manage_settings',
    'view_reports',
    'manage_roles',
    'system_access'
  ],
  ADMIN: [
    'manage_members',
    'manage_analytics',
    'view_reports',
    'limited_settings'
  ],
  MODERATOR: [
    'view_members',
    'view_analytics',
    'basic_reports'
  ]
} as const;

export type AdminRole = keyof typeof ADMIN_PERMISSIONS;

// Role hierarchy for authorization
export const ROLE_HIERARCHY = {
  SUPER_ADMIN: 3,
  ADMIN: 2,
  MODERATOR: 1
} as const;

export function canManageRole(userRole: AdminRole, targetRole: AdminRole): boolean {
  return ROLE_HIERARCHY[userRole] > ROLE_HIERARCHY[targetRole];
}

export function getRolePermissions(role: AdminRole): string[] {
  return ADMIN_PERMISSIONS[role] || [];
} 